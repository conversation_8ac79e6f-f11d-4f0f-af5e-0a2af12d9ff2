import { SEVERITIES } from '@/enums';
import {
  FooterToolbar,
  PageContainer,
  ProCard,
  ProColumns,
  ProDescriptions,
  ProForm,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Badge, Button, DatePicker, Space, Tabs, Tag, Tooltip } from 'antd';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';

const { RangePicker } = DatePicker;

const AlarmDetails: React.FC = () => {
  // const { id } = useParams();
  const formRef = useRef<ProFormInstance>();
  const [activeTab, setActiveTab] = useState('1');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

  // 模拟loading状态 - 实际项目中应该从API请求中获取
  const [loading, setLoading] = useState(true);

  // 模拟API请求完成后停止loading
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 2000); // 2秒后停止loading

    return () => clearTimeout(timer);
  }, []);

  // 模拟数据 - 实际项目中应该通过API获取
  const alarmData = {
    content: '磁盘使用率超过90%',
    level: '4',
    tags: [
      { tag: 'environment', value: 'production' },
      { tag: 'service', value: 'database' },
    ],
    alarmTime: '2024-01-15 14:30:25',
    alarmObject: 'DB-Server-01',
    alarmObjectId: 'host123',
    duration: '2小时30分钟',
    confirmStatus: '未确认',
    recoveryTime: '-',
  };

  const metricsData = [
    {
      id: '1',
      name: '磁盘使用率',
      itemId: 'item123',
      lastMonitorTime: '2024-01-15 16:45:30',
      monitorData: '92.5%',
      dataChange: '+2.3%',
    },
    {
      id: '2',
      name: 'CPU使用率',
      itemId: 'item124',
      lastMonitorTime: '2024-01-15 16:45:28',
      monitorData: '75.2%',
      dataChange: '+5.1%',
    },
  ];

  const actionRecordsData = [
    {
      id: '1',
      confirmTime: '2024-01-15 15:00:00',
      confirmUser: '张三',
      confirmMessage: '正在处理磁盘清理',
      confirmAction: '确认问题',
    },
  ];

  const notificationData = [
    {
      id: '1',
      time: '2024-01-15 14:31:00',
      type: '邮件',
      sendMethod: 'SMTP',
      receiver: '<EMAIL>',
      sendResult: '发送成功',
      triggerRule: '磁盘使用率>90%',
    },
    {
      id: '2',
      time: '2024-01-15 14:31:05',
      type: '短信',
      sendMethod: 'SMS',
      receiver: '138****8888',
      sendResult: '发送成功',
      triggerRule: '磁盘使用率>90%',
    },
  ];

  const historyData = [
    {
      id: '1',
      alarmTime: '2024-01-15 14:30:25',
      level: '4',
      eventId: 'event123',
      alarmName: '磁盘使用率超过90%',
      duration: '2小时30分钟',
      confirmed: '是',
      recoveryStatus: '未恢复',
      recoveryTime: '-',
      tags: [
        { tag: 'environment', value: 'production' },
        { tag: 'service', value: 'database' },
      ],
    },
    {
      id: '2',
      alarmTime: '2024-01-14 10:15:30',
      level: '3',
      eventId: 'event122',
      alarmName: '内存使用率超过80%',
      duration: '1小时15分钟',
      confirmed: '是',
      recoveryStatus: '已恢复',
      recoveryTime: '2024-01-14 11:30:45',
      tags: [{ tag: 'environment', value: 'production' }],
    },
  ];

  const metricsColumns: ProColumns[] = [
    {
      title: '指标名称',
      dataIndex: 'name',
      render: (text, record) => (
        <a
          onClick={() => {
            history.push(
              `/monitor-config/host/monitor-item/${alarmData.alarmObjectId}/view/${record.itemId}`,
            );
          }}
        >
          {text}
        </a>
      ),
    },
    {
      title: '最新监测时间',
      dataIndex: 'lastMonitorTime',
    },
    {
      title: '监控数据',
      dataIndex: 'monitorData',
    },
    {
      title: '数据变化量',
      dataIndex: 'dataChange',
      render: (text: any) => (
        <span style={{ color: String(text)?.startsWith('+') ? '#ff4d4f' : '#52c41a' }}>{text}</span>
      ),
    },
  ];

  const actionRecordsColumns: ProColumns[] = [
    {
      title: '确认时间',
      dataIndex: 'confirmTime',
    },
    {
      title: '确认人',
      dataIndex: 'confirmUser',
    },
    {
      title: '确认信息',
      dataIndex: 'confirmMessage',
    },
    {
      title: '确认动作',
      dataIndex: 'confirmAction',
    },
  ];

  const notificationColumns: ProColumns[] = [
    {
      title: '时间',
      dataIndex: 'time',
    },
    {
      title: '类型',
      dataIndex: 'type',
    },
    {
      title: '发送方式',
      dataIndex: 'sendMethod',
    },
    {
      title: '接收人',
      dataIndex: 'receiver',
    },
    {
      title: '发送结果',
      dataIndex: 'sendResult',
      render: (text) => <Badge status={text === '发送成功' ? 'success' : 'error'} text={text} />,
    },
    {
      title: '触发规则',
      dataIndex: 'triggerRule',
    },
  ];

  const historyColumns: ProColumns[] = [
    {
      title: '告警时间',
      dataIndex: 'alarmTime',
    },
    {
      title: '告警等级',
      dataIndex: 'level',
      render: (text) => {
        const severityObj = SEVERITIES.find((item) => item.value === text);
        return severityObj ? (
          <Tooltip title={severityObj.label}>
            <Tag color={severityObj.tagColor}>{severityObj.label}</Tag>
          </Tooltip>
        ) : (
          text
        );
      },
    },
    {
      title: 'ID',
      dataIndex: 'eventId',
    },
    {
      title: '告警名称',
      dataIndex: 'alarmName',
    },
    {
      title: '告警时长',
      dataIndex: 'duration',
    },
    {
      title: '确认问题',
      dataIndex: 'confirmed',
    },
    {
      title: '恢复状态',
      dataIndex: 'recoveryStatus',
      render: (text) => (
        <span>
          <Badge text={text} status={text === '已恢复' ? 'success' : 'error'} />
        </span>
      ),
    },
    {
      title: '恢复时间',
      dataIndex: 'recoveryTime',
    },
    {
      title: '标签',
      dataIndex: 'tags',
      render: (tags: any) => (
        <>
          {Array.isArray(tags) &&
            tags?.map((item: any, index: number) => (
              <Tag key={index}>
                {item.tag}: {item.value}
              </Tag>
            ))}
        </>
      ),
    },
    {
      title: '操作',
      width: 70,
      key: 'action',
      render: () => (
        <Button type="link" size="small">
          确认
        </Button>
      ),
    },
  ];

  const severityObj = SEVERITIES.find((item) => item.value === alarmData.level);

  return (
    <PageContainer header={{ title: false }}>
      <ProForm
        formRef={formRef}
        submitter={{
          searchConfig: {
            submitText: '确认告警',
            resetText: '关闭告警',
          },
          onReset: () => {
            // 关闭告警逻辑
            console.log('关闭告警');
          },
          render: (_, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
        }}
        onFinish={async () => {
          // 确认告警逻辑
          console.log('确认告警');
          return true;
        }}
      >
        <ProCard title="告警信息" style={{ borderRadius: 0 }} loading={loading}>
          <ProDescriptions column={3}>
            <ProDescriptions.Item label="告警内容">{alarmData.content}</ProDescriptions.Item>
            <ProDescriptions.Item label="告警等级">
              {severityObj ? (
                <div style={{ color: severityObj.tagColor }}>{severityObj.label}</div>
              ) : (
                alarmData.level
              )}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="标签">
              {alarmData.tags.map((item, index) => (
                <Tag key={index}>
                  {item.tag}: {item.value}
                </Tag>
              ))}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="告警时间">{alarmData.alarmTime}</ProDescriptions.Item>
            <ProDescriptions.Item label="告警对象">
              <a
                onClick={() => {
                  history.push(`/monitor-config/host/details/${alarmData.alarmObjectId}`);
                }}
              >
                {alarmData.alarmObject}
              </a>
            </ProDescriptions.Item>
            <ProDescriptions.Item label="告警时长">{alarmData.duration}</ProDescriptions.Item>
            <ProDescriptions.Item label="确认问题状态">
              {alarmData.confirmStatus}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="恢复时间">{alarmData.recoveryTime}</ProDescriptions.Item>
          </ProDescriptions>
        </ProCard>

        <ProCard title="监控指标" style={{ borderRadius: 0 }} loading={loading}>
          <ProTable
            className="inner-table"
            columns={metricsColumns}
            dataSource={metricsData}
            rowKey="id"
            search={false}
            pagination={false}
            options={false}
            size="small"
          />
        </ProCard>

        <ProCard style={{ borderRadius: 0 }} loading={loading}>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={[
              {
                key: '1',
                label: '告警动作记录',
                children: (
                  <ProTable
                    className="inner-table"
                    columns={actionRecordsColumns}
                    dataSource={actionRecordsData}
                    rowKey="id"
                    search={false}
                    pagination={false}
                    options={false}
                    size="small"
                  />
                ),
              },
              {
                key: '2',
                label: '告警通知',
                children: (
                  <ProTable
                    className="inner-table"
                    columns={notificationColumns}
                    dataSource={notificationData}
                    rowKey="id"
                    search={false}
                    pagination={false}
                    options={false}
                    size="small"
                  />
                ),
              },
              {
                key: '3',
                label: '告警历史',
                children: (
                  <div>
                    <div style={{ marginBottom: 16 }}>
                      <Space>
                        <span>时间范围：</span>
                        <RangePicker
                          showTime
                          value={dateRange}
                          onChange={(dates) =>
                            setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null)
                          }
                          format="YYYY-MM-DD HH:mm:ss"
                        />
                      </Space>
                    </div>
                    <ProTable
                      className="inner-table"
                      columns={historyColumns}
                      dataSource={historyData}
                      rowKey="id"
                      search={false}
                      pagination={{
                        pageSize: 10,
                        showSizeChanger: true,
                      }}
                      options={false}
                      size="small"
                    />
                  </div>
                ),
              },
              {
                key: '4',
                label: '处理建议',
                children: (
                  <div>
                    <div style={{ marginBottom: 24 }}>
                      <h4>处理建议</h4>
                      <p>
                        1. 立即检查磁盘使用情况，清理不必要的临时文件和日志文件。
                        <br />
                        2. 考虑扩展磁盘容量或迁移部分数据到其他存储设备。
                        <br />
                        3. 设置自动清理策略，定期清理过期数据。
                        <br />
                        4. 监控磁盘使用趋势，提前预警。
                      </p>
                    </div>
                    <div>
                      <h4>AI智能诊断</h4>
                      <p>
                        根据历史数据分析，该服务器磁盘使用率在过去7天内持续上升，主要原因是日志文件增长过快。
                        建议立即执行日志轮转策略，并考虑增加磁盘监控频率。预计在当前增长速度下，
                        磁盘将在48小时内达到95%使用率，届时可能影响系统正常运行。
                      </p>
                    </div>
                  </div>
                ),
              },
            ]}
          />
        </ProCard>
      </ProForm>
    </PageContainer>
  );
};

export default AlarmDetails;
